#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试钉钉表格单元格格式设置功能
"""

import json
from dingtalk_sheet_utils import DingTalkSheetUtils

def test_cell_format():
    """测试单元格格式设置"""
    try:
        # 加载配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 初始化钉钉表格工具
        dingtalk_sheet = DingTalkSheetUtils(config)
        
        # 获取工作表ID
        sheet_id = "st-959864b5-49951"  # 从之前的输出中获取
        
        # 测试数据：包含长数字
        test_data = [["测试长数字", "2853833415982401783", "普通文本"]]
        test_range = "A20:C20"  # 选择一个测试位置
        
        print("🧪 开始测试单元格格式设置...")
        print(f"📊 测试数据: {test_data}")
        print(f"📍 测试范围: {test_range}")
        
        # 1. 先写入数据
        print("\n1️⃣ 写入测试数据...")
        write_success = dingtalk_sheet.write_cell_range(sheet_id, test_range, test_data)
        
        if write_success:
            print("✅ 数据写入成功")
            
            # 2. 设置B20单元格（包含长数字）为文本格式
            print("\n2️⃣ 设置长数字单元格为文本格式...")
            format_success = dingtalk_sheet.set_cell_format(sheet_id, "B20", "@")
            
            if format_success:
                print("✅ 单元格格式设置成功")
                
                # 3. 重新写入数据，看看是否能保持文本格式
                print("\n3️⃣ 重新写入数据测试...")
                rewrite_success = dingtalk_sheet.write_cell_range(sheet_id, "B20", [["2853833415982401783"]])
                
                if rewrite_success:
                    print("✅ 重新写入成功")
                    print("\n🎯 请前往钉钉表格查看 B20 单元格，确认长数字是否以文本格式显示")
                else:
                    print("❌ 重新写入失败")
            else:
                print("❌ 单元格格式设置失败")
        else:
            print("❌ 数据写入失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    test_cell_format()
