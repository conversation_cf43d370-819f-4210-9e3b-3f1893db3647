#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采购单号检查器
功能：读取钉钉表格中的代发数据，统计当前日期的记录数和采购单号为空的记录数
"""

import json
import requests
import pandas as pd
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from dingtalk_sheet_utils import DingTalkSheetUtils
import calendar


# 注释掉钉钉数据读取部分 - 改为从Excel读取
# class PurchaseOrderChecker:
#     """采购单号检查器"""
#
#     def __init__(self, config_path: str = "config.json"):
#         """
#         初始化采购单号检查器
#
#         Args:
#             config_path: 配置文件路径
#         """
#         self.config_path = config_path
#         self.config = self._load_config()

#         # 从配置文件获取表格ID
#         purchase_config = self.config.get('purchase_checker', {})
#         self.source_sheet_id = purchase_config.get('source_sheet_id')
#         self.target_sheet_id = purchase_config.get('target_sheet_id')
#
#         # 获取其他配置
#         self.target_sheet_name_pattern = purchase_config.get('target_sheet_name_pattern', '{month}代发-1')
#         self.result_sheet_name = purchase_config.get('result_sheet_name', '采购部绩效明细')
#         self.last_row = purchase_config.get('last_row', 1)
#
#         self.column_keywords = purchase_config.get('column_keywords', {})
#         self.empty_values = purchase_config.get('empty_values', ['', 'none', 'null'])
#
#         # 获取当前环境（仅用于获取API密钥等基础配置）
#         current_env = self.config.get('dingtalk', {}).get('current_environment', 'test')
#         print(f"🔧 当前环境: {current_env}")
#
#         print(f"🔍 调试：配置文件中的表格ID: {self.target_sheet_id}")
#         print(f"🔍 调试：代发数据表格ID: {self.source_sheet_id}")
#
#         # 创建读取数据的配置
#         source_config = {
#             'dingtalk': {
#                 'current_environment': current_env,
#                 'sheet': {
#                     'app_key': self.config.get('dingtalk', {}).get('sheet', {}).get('app_key'),
#                     'app_secret': self.config.get('dingtalk', {}).get('sheet', {}).get('app_secret'),
#                     'operator_id': self.config.get('dingtalk', {}).get('sheet', {}).get('operator_id'),
#                     'test_sheet': {
#                         'sheet_id': self.source_sheet_id,
#                         'sheet_name': '仓库绩效明细',
#                         'sheet2_name': '仓库绩效汇总',
#                         'last_row': 1
#                     },
#                     'prod_sheet': {
#                         'sheet_id': self.source_sheet_id,
#                         'sheet_name': '仓库绩效明细',
#                         'sheet2_name': '仓库绩效汇总',
#                         'last_row': 1
#                     }
#                 }
#             }
#         }
#
#         # 创建写入数据的配置
#         target_config = {
#             'dingtalk': {
#                 'current_environment': current_env,
#                 'sheet': {
#                     'app_key': self.config.get('dingtalk', {}).get('sheet', {}).get('app_key'),
#                     'app_secret': self.config.get('dingtalk', {}).get('sheet', {}).get('app_secret'),
#                     'operator_id': self.config.get('dingtalk', {}).get('sheet', {}).get('operator_id'),
#                     'test_sheet': {
#                         'sheet_id': self.target_sheet_id,
#                         'sheet_name': '仓库绩效明细',
#                         'sheet2_name': '仓库绩效汇总',
#                         'last_row': 1
#                     },
#                     'prod_sheet': {
#                         'sheet_id': self.target_sheet_id,
#                         'sheet_name': '仓库绩效明细',
#                         'sheet2_name': '仓库绩效汇总',
#                         'last_row': 1
#                     }
#                 }
#             }
#         }
#
#         # 初始化两个钉钉表格工具实例
#         self.source_sheet = DingTalkSheetUtils(source_config)
#         self.dingtalk_sheet = DingTalkSheetUtils(target_config)
#
#         print(f"🔧 采购单号检查器初始化完成")
#         print(f"📋 代发数据表格ID: {self.source_sheet_id}")
#         print(f"📋 结果写入表格ID: {self.target_sheet_id}")
    
    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {str(e)}")
            return {}

    def _find_actual_last_row(self, target_sheet_id: str) -> int:
        """
        动态检测表格中实际的最后一行数据

        Args:
            target_sheet_id: 目标工作表ID

        Returns:
            实际的最后一行行号，如果表格为空返回0
        """
        try:
            # 读取表格的前200行数据来检测实际内容
            test_range = "A1:C200"
            print(f"🔍 检测表格实际数据行数 (范围: {test_range})...")

            raw_data = self.dingtalk_sheet.get_cell_range(target_sheet_id, test_range)
            if not raw_data:
                print("📋 表格为空或无法读取数据")
                return 0

            # 从后往前查找最后一个非空行
            last_row = 0
            for i in range(len(raw_data) - 1, -1, -1):
                row = raw_data[i]
                # 检查这一行是否有任何非空内容
                if row and any(str(cell).strip() for cell in row if cell is not None):
                    last_row = i + 1  # 行号从1开始
                    break

            print(f"📊 检测到实际最后数据行: {last_row}")
            return last_row

        except Exception as e:
            print(f"❌ 检测实际数据行数失败: {str(e)}")
            # 如果检测失败，返回配置文件中的值作为备用
            return self.last_row

    def get_current_month_info(self) -> Tuple[int, str, str]:
        """
        获取前一天的月份信息（用于读取和写入前一天的数据）

        Returns:
            (月份数字, 月份中文, 前一天日期中文格式)
        """
        # 获取前一天的日期
        yesterday = datetime.now() - timedelta(days=1)
        month_num = int(yesterday.month)
        month_cn = f"{month_num}月"
        current_date_cn = f"{month_num}月{yesterday.day}日"

        print(f"📅 处理前一天的数据: {yesterday.strftime('%Y-%m-%d')} ({current_date_cn})")
        return month_num, month_cn, current_date_cn
    
    def find_target_sheet(self, month_cn: str) -> Optional[Dict]:
        """
        查找目标工作表（查找"7月代发-1"这个具体的sheet页）

        Args:
            month_cn: 月份中文，如"7月"

        Returns:
            工作表信息字典，如果找不到返回None
        """
        try:
            # 获取代发数据表格的工作表列表
            sheets = self.source_sheet.get_sheets_list()
            if not sheets:
                print("❌ 无法获取代发数据表格的工作表列表")
                return None

            # 使用配置文件中的工作表名称模式
            target_sheet_name = self.target_sheet_name_pattern.format(month=month_cn)
            print(f"🔍 正在查找工作表: '{target_sheet_name}'...")

            # 查找匹配的工作表
            for sheet in sheets:
                sheet_name = sheet.get('name', '')
                if sheet_name == target_sheet_name:
                    print(f"✅ 找到目标工作表: {sheet_name} (ID: {sheet.get('id')})")
                    return sheet

            print(f"⚠️ 未找到工作表'{target_sheet_name}'")
            print("📋 可用的工作表列表:")
            for i, sheet in enumerate(sheets):
                print(f"  {i+1}. {sheet.get('name', 'N/A')} (ID: {sheet.get('id', 'N/A')})")

            return None

        except Exception as e:
            print(f"❌ 查找目标工作表失败: {str(e)}")
            return None


    def _find_date_range_in_sheet(self, sheet_id: str, target_date: str) -> tuple:
        """
        在工作表中查找包含指定日期的数据范围

        Args:
            sheet_id: 工作表ID
            target_date: 目标日期字符串，如"2025-07-12"

        Returns:
            (start_row, end_row, date_col_idx) 或 (None, None, None)
        """
        try:
            # 先读取前几行找到表头和日期列
            header_range = "A1:Z10"
            print(f"🔍 查找表头和日期列 (范围: {header_range})...")

            header_data = self.source_sheet.get_cell_range(sheet_id, header_range)
            if not header_data:
                print("❌ 无法读取表头数据")
                return None, None, None

            # 查找日期列
            date_col_idx = None
            date_keyword = self.column_keywords.get('date_column', '日期')

            for row_idx, row in enumerate(header_data):
                for col_idx, cell in enumerate(row):
                    if cell and date_keyword in str(cell):
                        date_col_idx = col_idx
                        print(f"✅ 找到日期列: 第{col_idx+1}列 ({cell})")
                        break
                if date_col_idx is not None:
                    break

            if date_col_idx is None:
                print(f"❌ 未找到包含'{date_keyword}'的列")
                return None, None, None

            # 读取日期列的更大范围来查找目标日期
            date_col_letter = chr(ord('A') + date_col_idx)
            date_range = f"{date_col_letter}1:{date_col_letter}1000"
            print(f"🔍 在日期列中查找目标日期 '{target_date}' (范围: {date_range})...")

            date_column_data = self.source_sheet.get_cell_range(sheet_id, date_range)
            if not date_column_data:
                print("❌ 无法读取日期列数据")
                return None, None, None

            # 查找包含目标日期的行范围
            start_row = None
            end_row = None

            for row_idx, row in enumerate(date_column_data):
                if row and len(row) > 0:
                    cell_value = str(row[0]).strip()
                    if target_date in cell_value:
                        if start_row is None:
                            start_row = row_idx + 1  # 行号从1开始
                        end_row = row_idx + 1

            if start_row is not None and end_row is not None:
                print(f"✅ 找到目标日期数据范围: 第{start_row}行到第{end_row}行 (共{end_row-start_row+1}行)")
                return start_row, end_row, date_col_idx
            else:
                print(f"⚠️ 未找到包含日期'{target_date}'的数据")
                return None, None, None

        except Exception as e:
            print(f"❌ 查找日期范围失败: {str(e)}")
            return None, None, None

    def read_sheet_data(self, sheet_id: str, target_date: str = None) -> List[List[str]]:
        """
        读取工作表数据，优先根据日期范围精确读取

        Args:
            sheet_id: 工作表ID
            target_date: 目标日期，如"2025-07-12"

        Returns:
            工作表数据的二维列表，如果失败返回空列表
        """
        try:
            # 如果提供了目标日期，先尝试精确查找日期范围
            if target_date:
                start_row, end_row, _ = self._find_date_range_in_sheet(sheet_id, target_date)

                if start_row is not None and end_row is not None:
                    # 扩展读取范围，包含表头和目标数据
                    read_start = max(1, start_row - 5)  # 向前读取几行确保包含表头
                    read_end = end_row + 5  # 向后读取几行确保完整

                    cell_range = f"A{read_start}:Z{read_end}"
                    print(f"📖 精确读取目标日期数据 (范围: {cell_range})...")

                    raw_data = self.source_sheet.get_cell_range(sheet_id, cell_range)
                    if raw_data:
                        print(f"✅ 成功读取到 {len(raw_data)} 行数据")
                        return raw_data

            # 如果精确读取失败，返回空列表而不是None，让程序继续执行
            print("❌ 精确读取失败，无法找到目标日期的数据！")
            print(f"💡 请检查工作表中是否存在日期为 '{target_date}' 的数据")
            print("⚠️ 未找到目标日期数据，将返回空结果")
            return []

        except Exception as e:
            print(f"❌ 读取工作表数据失败: {str(e)}")
            return []

    def find_column_indexes(self, data: List[List[str]]) -> Tuple[Optional[int], Optional[int], Optional[int], int]:
        """
        查找日期列、采购单号列和原单号列的索引，并返回实际的标题行索引

        Args:
            data: 表格数据（包含多行）

        Returns:
            (日期列索引, 采购单号列索引, 原单号列索引, 标题行索引)
        """
        date_col_idx = None
        purchase_col_idx = None
        original_col_idx = None
        header_row_idx = 0

        print("🔍 正在查找列索引和标题行...")

        # 从配置文件获取列关键词
        date_keyword = self.column_keywords.get('date_column', '日期')
        purchase_keyword = self.column_keywords.get('purchase_number_column', '采购单号')
        original_keyword = self.column_keywords.get('original_number_column', '原单号')

        # 检查是否有标准的标题行，如果没有则使用固定的列位置
        print("🔍 分析表格结构...")
        print(f"第1行数据: {data[0] if data else '无数据'}")

        # 检查第一行是否包含日期格式的数据（如"7月18日"）
        first_row = data[0] if data else []
        has_date_data = False

        if len(first_row) >= 2:
            second_col = str(first_row[1]).strip()
            # 检查第二列是否包含日期格式（如"7月18日"、"7月19日"等）
            if '月' in second_col and '日' in second_col:
                has_date_data = True
                print(f"✅ 检测到第2列包含日期数据: {second_col}")

        if has_date_data:
            # 这是一个没有标准标题行的数据表格，使用固定列位置
            print("📋 检测到无标题行的数据表格，使用固定列位置:")
            header_row_idx = -1  # 表示没有标题行，从第0行开始就是数据
            date_col_idx = 1      # 第2列是日期
            purchase_col_idx = 2  # 第3列是采购单号
            original_col_idx = 3  # 第4列是原单号
            print(f"  📅 日期列: 第{date_col_idx+1}列")
            print(f"  📋 采购单号列: 第{purchase_col_idx+1}列")
            print(f"  📄 原单号列: 第{original_col_idx+1}列")
        else:
            # 尝试查找标准的标题行
            for row_idx, row in enumerate(data[:5]):  # 只检查前5行
                if not row:
                    continue

                print(f"🔍 检查第{row_idx+1}行: {row}")

                # 检查这一行是否包含我们需要的列关键词
                found_keywords = 0
                temp_date_col = None
                temp_purchase_col = None
                temp_original_col = None

                for col_idx, cell in enumerate(row):
                    cell_str = str(cell).strip()

                    if date_keyword in cell_str:
                        temp_date_col = col_idx
                        found_keywords += 1
                        print(f"  ✅ 找到日期列关键词 '{date_keyword}' 在第{col_idx+1}列: {cell_str}")

                    if purchase_keyword in cell_str:
                        temp_purchase_col = col_idx
                        found_keywords += 1
                        print(f"  ✅ 找到采购单号列关键词 '{purchase_keyword}' 在第{col_idx+1}列: {cell_str}")

                    if original_keyword in cell_str:
                        temp_original_col = col_idx
                        found_keywords += 1
                        print(f"  ✅ 找到原单号列关键词 '{original_keyword}' 在第{col_idx+1}列: {cell_str}")

                print(f"  📊 本行找到 {found_keywords} 个关键词")

                # 如果找到了至少1个关键词，先记录下来，如果找到2个或以上就确定为标题行
                if found_keywords >= 1:
                    if found_keywords >= 2 or row_idx == 0:  # 如果是第一行且至少有1个关键词，也认为是标题行
                        header_row_idx = row_idx
                        date_col_idx = temp_date_col
                        purchase_col_idx = temp_purchase_col
                        original_col_idx = temp_original_col
                        print(f"✅ 确定标题行: 第{row_idx+1}行 (找到{found_keywords}个关键词)")
                        break

        if date_col_idx is not None:
            print(f"  � 日期列: 第{date_col_idx+1}列")
        if purchase_col_idx is not None:
            print(f"  📋 采购单号列: 第{purchase_col_idx+1}列")
        if original_col_idx is not None:
            print(f"  📄 原单号列: 第{original_col_idx+1}列")

        return date_col_idx, purchase_col_idx, original_col_idx, header_row_idx

    def analyze_data(self, data: List[List[str]], current_date_cn: str,
                    date_col_idx: int, purchase_col_idx: int, original_col_idx: int, has_header: bool = True) -> Dict:
        """
        分析数据，统计当前日期的记录数和采购单号为空的记录数

        Args:
            data: 工作表数据
            current_date_cn: 当前日期中文格式，如"7月3日"
            date_col_idx: 日期列索引
            purchase_col_idx: 采购单号列索引
            original_col_idx: 原单号列索引

        Returns:
            分析结果字典
        """
        total_current_date_records = 0
        empty_purchase_records = 0
        empty_purchase_original_numbers = []
        all_dates_found = set()

        print(f"🔍 开始分析数据，查找日期为'{current_date_cn}'的记录...")

        # 根据是否有标题行决定从哪一行开始分析
        start_row = 1 if has_header else 0
        for row_idx, row in enumerate(data[start_row:], start=start_row+1):
            if len(row) <= max(date_col_idx, purchase_col_idx, original_col_idx):
                continue

            # 获取日期值
            date_value = str(row[date_col_idx]).strip() if date_col_idx < len(row) else ""

            # 收集所有找到的日期
            if date_value:
                all_dates_found.add(date_value)

            # 检查日期是否匹配（不考虑年份）
            if date_value == current_date_cn:
                total_current_date_records += 1

                # 获取采购单号值
                purchase_value = str(row[purchase_col_idx]).strip() if purchase_col_idx < len(row) else ""

                # 检查采购单号是否为空（使用配置文件中的空值列表）
                if not purchase_value or purchase_value.lower() in self.empty_values:
                    empty_purchase_records += 1

                    # 获取对应的原单号
                    original_value = str(row[original_col_idx]).strip() if original_col_idx < len(row) else ""
                    if original_value:
                        empty_purchase_original_numbers.append(original_value)
                        print(f"  📝 第{row_idx}行: 日期={date_value}, 采购单号=空, 原单号={original_value}")

        # 显示表格中所有找到的日期
        if all_dates_found:
            print(f"📅 表格中找到的所有日期: {sorted(list(all_dates_found))}")

        if total_current_date_records == 0:
            print(f"🤷‍♀️ 在代发数据源中未找到日期为 '{current_date_cn}' 的记录。")
            return {'no_data': True, 'current_date': current_date_cn}
            
        result = {
            'current_date': current_date_cn,
            'total_records': total_current_date_records,
            'empty_purchase_records': empty_purchase_records,
            'empty_purchase_original_numbers': empty_purchase_original_numbers,
        }

        print(f"\n📊 分析结果:")
        print(f"  - 当前日期总记录数: {total_current_date_records}")
        print(f"  - 平台代发订单-未改单数: {empty_purchase_records}")
        print(f"  - 平台代发订单-未改单原始单号: {empty_purchase_original_numbers}")

        return result
    
    def _find_monthly_table(self, sheet_id: str, table_title: str) -> Optional[int]:
        """在工作表中查找月度汇总表的标题行号"""
        try:
            range_to_scan = "A1:A200"
            print(f"🔍 在工作表(ID:{sheet_id})中扫描标题'{table_title}' (范围:{range_to_scan})...")
            column_data = self.dingtalk_sheet.get_cell_range(sheet_id, range_to_scan)
            if column_data:
                for i, row in enumerate(column_data):
                    if row and table_title in str(row[0]):
                        title_row_index = i + 1
                        print(f"✅ 找到月度汇总表标题，在第 {title_row_index} 行")
                        return title_row_index
            print(f"🤷‍♀️ 未找到标题为 '{table_title}' 的月度汇总表")
            return None
        except Exception as e:
            print(f"❌ 查找月度汇总表失败: {e}")
            return None

    def _find_date_row(self, sheet_id: str, target_date: str, start_row: int) -> Optional[int]:
        """
        在工作表中查找指定日期对应的行号

        Args:
            sheet_id: 工作表ID
            target_date: 目标日期，如"7月22日"
            start_row: 开始搜索的行号

        Returns:
            找到的行号，如果未找到返回None
        """
        try:
            # 搜索A列中的日期
            search_range = f"A{start_row}:A100"
            print(f"🔍 在A列中查找日期 '{target_date}' (范围: {search_range})...")

            column_data = self.dingtalk_sheet.get_cell_range(sheet_id, search_range)
            if column_data:
                for i, row in enumerate(column_data):
                    if row and len(row) > 0:
                        cell_value = str(row[0]).strip()
                        if target_date in cell_value or cell_value == target_date:
                            found_row = start_row + i
                            print(f"✅ 找到日期 '{target_date}' 在第 {found_row} 行")
                            return found_row

            print(f"⚠️ 未找到日期 '{target_date}' 对应的行")
            return None

        except Exception as e:
            print(f"❌ 查找日期行失败: {str(e)}")
            return None

    def write_monthly_summary(self, combined_data: Dict) -> bool:
        """
        将合并后的日度数据写入月度汇总表。

        Args:
            combined_data: 包含当天所有指标的字典

        Returns:
            是否写入成功
        """
        try:
            print("📝 准备写入月度汇总报告...")

            sheets = self.dingtalk_sheet.get_sheets_list()
            if not sheets:
                print("❌ 无法获取目标工作簿的工作表列表")
                return False

            target_sheet_info = next((s for s in sheets if self.result_sheet_name in s.get('name', '')), None)
            if not target_sheet_info:
                print(f"❌ 在目标工作簿中未找到名为'{self.result_sheet_name}'的工作表！")
                return False
            
            target_sheet_id = target_sheet_info['id']
            print(f"✅ 找到结果工作表: {target_sheet_info['name']} (ID: {target_sheet_id})")

            # 获取前一天的日期
            yesterday = datetime.now() - timedelta(days=1)
            year, month, day = yesterday.year, yesterday.month, yesterday.day
            table_title = f"{year}年{month:02d}月数据汇总"
            headers = [
                "日期", "当前日期总记录数", "平台代发订单-未改单数",
                "平台代发订单-未改单原始单号", "断货阶段未备注",
                "改动成本错误或未改单数", "改动成本错误或未改单实际总数",
                "报价严重超时", "报价超时"
            ]
            
            title_row_num = self._find_monthly_table(target_sheet_id, table_title)
            
            header_row_num = 0
            if title_row_num is None:
                print(f"📋 未找到当月表格，将创建新表格...")
                last_row = self._find_actual_last_row(target_sheet_id)
                start_row = last_row + 3 if last_row > 0 else 1
                
                # 写标题
                title_range = f"A{start_row}:{chr(ord('A') + len(headers) - 1)}{start_row}"
                self.dingtalk_sheet.write_cell_range(target_sheet_id, f"A{start_row}", [[table_title]])
                self.dingtalk_sheet._merge_cells(target_sheet_id, title_range, table_title)
                
                # 写表头
                header_row_num = start_row + 1
                header_range = f"A{header_row_num}:{chr(ord('A') + len(headers) - 1)}{header_row_num}"
                self.dingtalk_sheet.write_cell_range(target_sheet_id, header_range, [headers])
                
                # 预填整月的日期列
                _, num_days = calendar.monthrange(year, month)
                date_column_data = [[f"{month}月{d}日"] for d in range(1, num_days + 1)]
                date_column_range = f"A{header_row_num + 1}:A{header_row_num + num_days}"
                self.dingtalk_sheet.write_cell_range(target_sheet_id, date_column_range, date_column_data)
                
                print(f"✅ 已在第 {start_row} 行创建新表格，并填充了 {num_days} 天的日期。")
            else:
                header_row_num = title_row_num + 1

            # 准备要写入的行数据，即使数据不全也用默认值填充
            row_data_values = []
            
            # 从第2列开始的数据
            row_data_values.append(str(combined_data.get('total_records', '暂无数据')))
            row_data_values.append(str(combined_data.get('empty_purchase_records', '暂无数据')))
            original_numbers = combined_data.get('empty_purchase_original_numbers', [])
            row_data_values.append("\n".join(original_numbers) if original_numbers else "无")
            row_data_values.append(str(combined_data.get('negative_stock_empty_remark_records', 0)))
            row_data_values.append(str(combined_data.get('cost_error_count_records', 0)))
            row_data_values.append(str(combined_data.get('cost_error_total_records', 0)))
            row_data_values.append(str(combined_data.get('quote_serious_timeout_records', 0)))
            row_data_values.append(str(combined_data.get('quote_timeout_records', 0)))

            # 查找当天日期对应的行号
            current_date_str = f"{month}月{day}日"
            target_row_num = self._find_date_row(target_sheet_id, current_date_str, header_row_num)

            if target_row_num is None:
                print(f"❌ 未找到日期 '{current_date_str}' 对应的行")
                return False

            target_range = f"B{target_row_num}:{chr(ord('A') + len(headers) - 1)}{target_row_num}"
            
            print(f"✍️ 正在写入数据到第 {target_row_num} 行 (范围: {target_range})...")
            # 只更新B列及之后的数据，不覆盖A列的日期
            success = self.dingtalk_sheet.write_cell_range(target_sheet_id, target_range, [row_data_values])

            if success:
                print(f"✅ 成功写入数据到 {target_range}")
                return True
            else:
                print(f"❌ 写入数据失败 at {target_range}")
                return False

        except Exception as e:
            print(f"❌ 写入月度汇总失败: {str(e)}")
            return False

    def run_check(self) -> Dict:
        """
        运行完整的检查流程

        Returns:
            检查结果字典，始终返回有效数据结构
        """
        try:
            print("🚀 开始执行采购单号检查...")

            # 1. 获取当前月份信息
            _, month_cn, current_date_cn = self.get_current_month_info()

            # 2. 查找目标工作表
            target_sheet = self.find_target_sheet(month_cn)
            if not target_sheet:
                print("⚠️ 未找到目标工作表，返回默认值")
                return {
                    'current_date': current_date_cn,
                    'total_records': '暂无数据',
                    'empty_purchase_records': '暂无数据',
                    'empty_purchase_original_numbers': []
                }

            sheet_id = target_sheet.get('id')
            sheet_name = target_sheet.get('name')

            # 3. 读取工作表数据
            data = self.read_sheet_data(sheet_id, current_date_cn)

            if not data:
                print("⚠️ 未找到目标日期数据，返回默认值")
                return {
                    'current_date': current_date_cn,
                    'total_records': '暂无数据',
                    'empty_purchase_records': '暂无数据',
                    'empty_purchase_original_numbers': []
                }

            # 4. 查找列索引和标题行
            date_col_idx, purchase_col_idx, original_col_idx, header_row_idx = self.find_column_indexes(data)

            if date_col_idx is None or purchase_col_idx is None or original_col_idx is None:
                print("❌ 未能找到所有必需的列（日期、采购单号、原单号），返回默认值")
                return {
                    'current_date': current_date_cn,
                    'total_records': '暂无数据',
                    'empty_purchase_records': '暂无数据',
                    'empty_purchase_original_numbers': []
                }

            # 5. 分析数据
            if header_row_idx == -1:
                # 没有标题行，从第0行开始就是数据
                result = self.analyze_data(data, current_date_cn, date_col_idx, purchase_col_idx, original_col_idx, has_header=False)
            else:
                # 有标题行，从标题行之后开始
                result = self.analyze_data(data[header_row_idx:], current_date_cn, date_col_idx, purchase_col_idx, original_col_idx, has_header=True)

            # 如果分析结果表明没有数据，返回默认值结构
            if result.get('no_data', False):
                return {
                    'current_date': current_date_cn,
                    'total_records': '暂无数据',
                    'empty_purchase_records': '暂无数据',
                    'empty_purchase_original_numbers': []
                }

            result['sheet_name'] = sheet_name
            result['sheet_id'] = sheet_id

            print("✅ 采购单号分析完成!")
            return result

        except Exception as e:
            print(f"❌ 采购单号检查失败: {str(e)}")
            # 即使出现异常，也返回默认值结构
            _, _, current_date_cn = self.get_current_month_info()
            return {
                'current_date': current_date_cn,
                'total_records': '暂无数据',
                'empty_purchase_records': '暂无数据',
                'empty_purchase_original_numbers': []
            }


        class ExcelStockChecker:
            """Excel库存检查器"""

    def __init__(self, config_path: str = "config.json"):
        """
        初始化Excel库存检查器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()

        # 从配置文件获取Excel相关配置
        excel_config = self.config.get('excel_checker', {})
        self.file_path = excel_config.get('file_path')
        self.sheet_name = excel_config.get('sheet_name', '采购部')
        self.column_keywords = excel_config.get('column_keywords', {})
        self.empty_values = excel_config.get('empty_values', ['', 'none', 'null', 'nan'])
        
        print(f"📁 Excel文件路径: {self.file_path}")
        print(f"📄 目标工作表: {self.sheet_name}")
        print("✅ Excel库存检查器初始化完成")

    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {str(e)}")
            return {}

    def get_current_date_info(self) -> Tuple[str, str]:
        """
        获取前一天的日期信息（用于读取和写入前一天的数据）

        Returns:
            (标准日期格式, 中文日期格式)
        """
        # 获取前一天的日期
        yesterday = datetime.now() - timedelta(days=1)
        standard_date = yesterday.strftime('%Y-%m-%d')
        month_num = int(yesterday.month)
        current_date_cn = f"{month_num}月{yesterday.day}日"

        print(f"📅 处理前一天的数据: {standard_date} ({current_date_cn})")
        return standard_date, current_date_cn

    def read_excel_file(self) -> Optional[pd.DataFrame]:
        """
        读取Excel文件

        Returns:
            DataFrame或None
        """
        try:
            if not os.path.exists(self.file_path):
                print(f"❌ Excel文件不存在: {self.file_path}")
                return None

            print(f"📖 正在读取Excel文件: {self.file_path}")
            print(f"📄 目标工作表: {self.sheet_name}")

            # 读取Excel文件
            df = pd.read_excel(self.file_path, sheet_name=self.sheet_name)

            if df.empty:
                print("⚠️ Excel工作表为空")
                return None

            print(f"✅ 成功读取Excel文件，共{len(df)}行数据")
            print(f"📋 列名: {list(df.columns)}")

            return df

        except Exception as e:
            print(f"❌ 读取Excel文件失败: {str(e)}")
            return None

    def find_column_indexes(self, df: pd.DataFrame) -> Tuple[Optional[str], Optional[str], Optional[str], Optional[str], Optional[str], Optional[str], Optional[str]]:
        """
        查找目标列名

        Args:
            df: DataFrame

        Returns:
            (日期列名, 可发库存列名, 备注列名, 改动成本错误或未改单数列名, 改动成本错误或未改单实际总数列名, 报价严重超时列名, 报价超时列名)
        """
        date_keyword = self.column_keywords.get('date_column', '日期')
        stock_keyword = self.column_keywords.get('stock_column', '可发库存')
        remark_keyword = self.column_keywords.get('remark_column', '备注')
        cost_error_count_keyword = self.column_keywords.get('cost_error_count_column', '改动成本错误或未改单数')
        cost_error_total_keyword = self.column_keywords.get('cost_error_total_column', '改动成本错误或未改单实际总数')
        quote_serious_timeout_keyword = self.column_keywords.get('quote_serious_timeout_column', '报价严重超时')
        quote_timeout_keyword = self.column_keywords.get('quote_timeout_column', '报价超时')

        date_col, stock_col, remark_col, cost_error_count_col, cost_error_total_col, quote_serious_timeout_col, quote_timeout_col = None, None, None, None, None, None, None

        print("🔍 查找目标列...")

        for col in df.columns:
            col_str = str(col).strip()

            if date_keyword in col_str and date_col is None:
                date_col = col
                print(f"  📅 日期列: {col}")

            if stock_keyword in col_str and stock_col is None:
                stock_col = col
                print(f"  📦 可发库存列: {col}")

            if remark_keyword in col_str and remark_col is None:
                remark_col = col
                print(f"  📝 备注列: {col}")

            if cost_error_count_keyword in col_str and cost_error_count_col is None:
                cost_error_count_col = col
                print(f"  - 改动成本错误或未改单数列: {col}")

            if cost_error_total_keyword in col_str and cost_error_total_col is None:
                cost_error_total_col = col
                print(f"  - 改动成本错误或未改单实际总数列: {col}")

            if quote_serious_timeout_keyword in col_str and quote_serious_timeout_col is None:
                quote_serious_timeout_col = col
                print(f"  - 报价严重超时列: {col}")

            if quote_timeout_keyword in col_str and quote_timeout_col is None:
                quote_timeout_col = col
                print(f"  - 报价超时列: {col}")

        return date_col, stock_col, remark_col, cost_error_count_col, cost_error_total_col, quote_serious_timeout_col, quote_timeout_col

    def analyze_stock_data(self, df: pd.DataFrame, target_date_cn: str,
                          date_col: str, stock_col: Optional[str], remark_col: str,
                          cost_error_count_col: str, cost_error_total_col: str,
                          quote_serious_timeout_col: Optional[str], quote_timeout_col: Optional[str]) -> Dict:
        """
        分析库存数据，查找符合条件的记录

        Args:
            df: DataFrame
            ...

        Returns:
            分析结果字典
        """
        print(f"🔍 开始分析Excel数据，查找日期为'{target_date_cn}'的记录...")

        total_target_date_records = 0
        negative_stock_empty_remark_records = 0
        cost_error_count_records = 0
        cost_error_total_records = 0
        quote_serious_timeout_records = 0
        quote_timeout_records = 0
        all_dates_found = set()

        # 遍历数据行
        for index, row in df.iterrows():
            try:
                # 获取日期值
                date_value = str(row[date_col]).strip() if pd.notna(row[date_col]) else ""

                # 收集所有找到的日期
                if date_value:
                    all_dates_found.add(date_value)

                # 检查日期是否匹配（支持多种日期格式）
                date_matches = False
                print(f"🔍 检查行{index+1}: 日期值='{date_value}', 目标日期='{target_date_cn}'")

                if date_value == target_date_cn:
                    date_matches = True
                    print(f"  ✅ 精确匹配")
                elif target_date_cn in date_value:  # 支持 "7月22日" 在 "2025-07-22 00:00:00" 中匹配
                    date_matches = True
                    print(f"  ✅ 包含匹配")
                elif date_value.startswith('2025-07-22'):  # 直接匹配日期部分
                    date_matches = True
                    print(f"  ✅ 2025-07-22格式匹配")
                elif date_value.startswith('2025/7/22'):  # 支持 "2025/7/22" 格式
                    date_matches = True
                    print(f"  ✅ 2025/7/22格式匹配")
                elif '2025/7/22' in date_value:  # 支持包含 "2025/7/22" 的格式
                    date_matches = True
                    print(f"  ✅ 包含2025/7/22格式匹配")
                else:
                    print(f"  ❌ 无匹配")

                if date_matches:
                    total_target_date_records += 1
                    print(f"  📊 匹配成功，开始分析该行数据...")

                    # 获取备注值
                    remark_value = str(row[remark_col]).strip() if pd.notna(row[remark_col]) else ""
                    is_empty_remark = remark_value in self.empty_values or remark_value == ""
                    print(f"    📝 备注值: '{remark_value}', 是否为空: {is_empty_remark}")

                    # 检查条件1：可发库存 < 0 且备注为空（仅当可发库存列存在时）
                    if stock_col is not None:
                        stock_value = row[stock_col] if pd.notna(row[stock_col]) else 0
                        print(f"    📦 可发库存值: {stock_value}")
                        try:
                            if float(stock_value) < 0 and is_empty_remark:
                                negative_stock_empty_remark_records += 1
                                print(f"    ✅ 断货阶段未备注 +1")
                        except (ValueError, TypeError):
                            pass

                    # 检查条件2：改动成本错误或未改单数 > 0（直接统计，不检查备注）
                    cost_error_count_value = row[cost_error_count_col] if pd.notna(row[cost_error_count_col]) else 0
                    print(f"    💰 改动成本错误或未改单数值: {cost_error_count_value}")
                    try:
                        if float(cost_error_count_value) > 0:
                            cost_error_count_records = int(float(cost_error_count_value))
                            print(f"    ✅ 改动成本错误或未改单数: {cost_error_count_records}")
                        else:
                            print(f"    ❌ 改动成本错误或未改单数为0或负数")
                    except (ValueError, TypeError) as e:
                        print(f"    ❌ 改动成本错误或未改单数转换失败: {e}")

                    # 检查条件3：改动成本错误或未改单实际总数 > 0（直接统计，不检查备注）
                    cost_error_total_value = row[cost_error_total_col] if pd.notna(row[cost_error_total_col]) else 0
                    print(f"    💰 改动成本错误或未改单实际总数值: {cost_error_total_value}")
                    try:
                        if float(cost_error_total_value) > 0:
                            cost_error_total_records = int(float(cost_error_total_value))
                            print(f"    ✅ 改动成本错误或未改单实际总数: {cost_error_total_records}")
                        else:
                            print(f"    ❌ 改动成本错误或未改单实际总数为0或负数")
                    except (ValueError, TypeError) as e:
                        print(f"    ❌ 改动成本错误或未改单实际总数转换失败: {e}")

                    # 检查条件4：报价严重超时（直接统计，不检查备注）
                    if quote_serious_timeout_col is not None:
                        quote_serious_timeout_value = row[quote_serious_timeout_col] if pd.notna(row[quote_serious_timeout_col]) else 0
                        print(f"    ⏰ 报价严重超时值: {quote_serious_timeout_value}")
                        try:
                            if float(quote_serious_timeout_value) > 0:
                                quote_serious_timeout_records = int(float(quote_serious_timeout_value))
                                print(f"    ✅ 报价严重超时: {quote_serious_timeout_records}")
                            else:
                                print(f"    ❌ 报价严重超时为0或负数")
                        except (ValueError, TypeError) as e:
                            print(f"    ❌ 报价严重超时转换失败: {e}")

                    # 检查条件5：报价超时（直接统计，不检查备注）
                    if quote_timeout_col is not None:
                        quote_timeout_value = row[quote_timeout_col] if pd.notna(row[quote_timeout_col]) else 0
                        print(f"    ⏰ 报价超时值: {quote_timeout_value}")
                        try:
                            if float(quote_timeout_value) > 0:
                                quote_timeout_records = int(float(quote_timeout_value))
                                print(f"    ✅ 报价超时: {quote_timeout_records}")
                            else:
                                print(f"    ❌ 报价超时为0或负数")
                        except (ValueError, TypeError) as e:
                            print(f"    ❌ 报价超时转换失败: {e}")

            except Exception as e:
                print(f"⚠️ 处理第{index+2}行数据时出错: {str(e)}")
                continue

        # 显示Excel中所有找到的日期
        if all_dates_found:
            print(f"📅 Excel中找到的所有日期: {sorted(list(all_dates_found))}")
        
        if total_target_date_records == 0:
            print(f"🤷‍♀️ 在Excel中未找到日期为 '{target_date_cn}' 的记录。")
            return {'no_data': True, 'current_date': target_date_cn}

        result = {
            'current_date': target_date_cn,
            'negative_stock_empty_remark_records': negative_stock_empty_remark_records,
            'cost_error_count_records': cost_error_count_records,
            'cost_error_total_records': cost_error_total_records,
            'quote_serious_timeout_records': quote_serious_timeout_records,
            'quote_timeout_records': quote_timeout_records,
        }

        print(f"\n📊 Excel分析结果:")
        print(f"  - 断货阶段未备注: {negative_stock_empty_remark_records}")
        print(f"  - 改动成本错误或未改单数: {cost_error_count_records}")
        print(f"  - 改动成本错误或未改单实际总数: {cost_error_total_records}")
        print(f"  - 报价严重超时: {quote_serious_timeout_records}")
        print(f"  - 报价超时: {quote_timeout_records}")

        return result

    def run_check(self) -> Dict:
        """
        运行Excel库存检查

        Returns:
            检查结果字典，始终返回有效数据结构
        """
        try:
            print("🚀 开始执行Excel库存检查...")

            # 1. 获取当前日期信息
            _, current_date_cn = self.get_current_date_info()

            # 2. 读取Excel文件
            df = self.read_excel_file()
            if df is None:
                print("⚠️ Excel文件读取失败，返回默认值")
                return {
                    'current_date': current_date_cn,
                    'negative_stock_empty_remark_records': 0,
                    'cost_error_count_records': 0,
                    'cost_error_total_records': 0,
                    'quote_serious_timeout_records': 0,
                    'quote_timeout_records': 0
                }

            # 3. 查找列索引
            date_col, stock_col, remark_col, cost_error_count_col, cost_error_total_col, quote_serious_timeout_col, quote_timeout_col = self.find_column_indexes(df)

            # 检查必需的列（可发库存列、报价相关列为可选）
            required_cols = [date_col, remark_col, cost_error_count_col, cost_error_total_col]
            if any(col is None for col in required_cols):
                missing_cols = []
                if date_col is None: missing_cols.append("日期")
                if remark_col is None: missing_cols.append("备注")
                if cost_error_count_col is None: missing_cols.append("改动成本错误或未改单数")
                if cost_error_total_col is None: missing_cols.append("改动成本错误或未改单实际总数")

                print(f"❌ 未能找到必需的列: {', '.join(missing_cols)}，返回默认值")
                return {
                    'current_date': current_date_cn,
                    'negative_stock_empty_remark_records': 0,
                    'cost_error_count_records': 0,
                    'cost_error_total_records': 0,
                    'quote_serious_timeout_records': 0,
                    'quote_timeout_records': 0
                }

            # 可选列的提示
            if stock_col is None:
                print("⚠️ 未找到'可发库存'列，将跳过库存相关检查")
            if quote_serious_timeout_col is None:
                print("⚠️ 未找到'报价严重超时'列，将跳过相关检查")
            if quote_timeout_col is None:
                print("⚠️ 未找到'报价超时'列，将跳过相关检查")

            # 4. 分析数据
            result = self.analyze_stock_data(df, current_date_cn, date_col, stock_col, remark_col, cost_error_count_col, cost_error_total_col, quote_serious_timeout_col, quote_timeout_col)

            # 如果分析结果表明没有数据，返回默认值结构
            if result.get('no_data', False):
                return {
                    'current_date': current_date_cn,
                    'negative_stock_empty_remark_records': 0,
                    'cost_error_count_records': 0,
                    'cost_error_total_records': 0,
                    'quote_serious_timeout_records': 0,
                    'quote_timeout_records': 0
                }

            print("✅ Excel库存分析完成!")
            return result

        except Exception as e:
            print(f"❌ Excel库存检查失败: {str(e)}")
            # 即使出现异常，也返回默认值结构
            _, current_date_cn = self.get_current_date_info()
            return {
                'current_date': current_date_cn,
                'negative_stock_empty_remark_records': 0,
                'cost_error_count_records': 0,
                'cost_error_total_records': 0,
                'quote_serious_timeout_records': 0,
                'quote_timeout_records': 0
            }


def main():
    """主函数"""
    print("=" * 80)
    print("🔍 采购部绩效数据拉取与汇总系统")
    print("=" * 80)

    # 1. 运行代发数据检查器
    print("\n" + "=" * 60)
    print("📋 第一步：拉取平台代发数据")
    print("=" * 60)

    purchase_checker = PurchaseOrderChecker()
    purchase_result = purchase_checker.run_check()

    # 2. 运行Excel库存检查器
    print("\n" + "=" * 60)
    print("📦 第二步：拉取本地Excel数据")
    print("=" * 60)

    excel_checker = ExcelStockChecker()
    excel_result = excel_checker.run_check()

    # 3. 合并数据并写入表格
    print("\n" + "=" * 60)
    print("✍️ 第三步：合并数据并写入月度汇总")
    print("=" * 60)

    # 合并两个来源的数据 - 现在两个检查器都会返回有效的数据结构
    combined_data = {}
    if purchase_result:
        combined_data.update(purchase_result)
    if excel_result:
        combined_data.update(excel_result)

    # 如果合并后的数据为空（理论上不应该发生），提供默认值
    if not combined_data:
        print("🤷‍♀️ 所有数据源均未找到今日数据或执行失败，将写入默认值。")
        combined_data = {
            'total_records': '暂无数据',
            'empty_purchase_records': '暂无数据',
            'empty_purchase_original_numbers': [],
            'negative_stock_empty_remark_records': 0,
            'cost_error_count_records': 0,
            'cost_error_total_records': 0,
            'quote_serious_timeout_records': 0,
            'quote_timeout_records': 0
        }

    # 总是尝试写入
    write_success = purchase_checker.write_monthly_summary(combined_data)
    
    if write_success:
        print("\n✅ 当日数据已成功写入/更新到月度汇总")
    else:
        print("\n❌ 写入月度汇总失败")

    print("\n" + "=" * 80)
    print("🎯 请前往钉钉表格查看'采购部绩效明细'工作表获取最新汇总。")


if __name__ == "__main__":
    main()
