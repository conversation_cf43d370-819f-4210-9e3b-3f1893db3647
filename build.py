#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采购部绩效数据拉取系统打包脚本
使用 PyInstaller 将 Python 程序打包成 exe 文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查是否安装了 PyInstaller"""
    try:
        import PyInstaller
        print("✅ PyInstaller 已安装")
        return True
    except ImportError:
        print("❌ PyInstaller 未安装")
        print("正在安装 PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller 安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller 安装失败")
            return False

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理spec文件
    spec_files = ['build_spec.spec', '*.spec']
    for spec_pattern in spec_files:
        if '*' in spec_pattern:
            import glob
            for spec_file in glob.glob(spec_pattern):
                if os.path.exists(spec_file):
                    os.remove(spec_file)
                    print(f"🧹 清理文件: {spec_file}")
        else:
            if os.path.exists(spec_pattern):
                os.remove(spec_pattern)
                print(f"🧹 清理文件: {spec_pattern}")

def create_spec_file():
    """创建 PyInstaller 规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['purchase_checker_simplified.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'pandas',
        'requests',
        'openpyxl',
        'urllib3',
        'dingtalk_sheet_utils',
        'pkg_resources',
        'pkg_resources.py2_warn',
        'importlib_metadata',
        'zipp',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除大型可选库
        'matplotlib',
        'scipy',
        'numpy.distutils',
        'numpy.f2py',
        'numpy.testing',
        'pandas.plotting',
        'pandas.tests',
        'pandas.io.clipboard',
        'pandas.io.html',
        'pandas.io.sql',
        'pandas.io.parquet',
        'pandas.io.feather',
        'xlwt',
        'xlsxwriter',
        'IPython',
        'jupyter',
        'notebook',
        'tkinter',
        'turtle',
        'pydoc',
        'doctest',
        'unittest',
        'test',
        'tests',
        'pytest',
        'coverage',
        'setuptools',
        'pip',
        'wheel',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='采购绩效数据同步',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('build_spec.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 创建规格文件: build_spec.spec")

def build_exe():
    """构建 exe 文件"""
    print("🔨 开始构建 exe 文件...")
    
    try:
        # 使用规格文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "build_spec.spec"]
        subprocess.check_call(cmd)
        print("✅ exe 文件构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def copy_config_files():
    """复制配置文件到输出目录"""
    dist_dir = Path("dist")
    if dist_dir.exists():
        config_files = ['config.json']  # 只复制config.json，dingtalk_sheet_utils.py已打包进exe
        for config_file in config_files:
            if os.path.exists(config_file):
                shutil.copy2(config_file, dist_dir)
                print(f"📋 复制配置文件: {config_file}")

def verify_required_files():
    """验证必需文件是否存在"""
    required_files = [
        'purchase_checker_simplified.py',
        'dingtalk_sheet_utils.py',
        'config.json'
    ]
    
    missing_files = []
    for file_name in required_files:
        if not os.path.exists(file_name):
            missing_files.append(file_name)
    
    if missing_files:
        print("❌ 缺少必需文件:")
        for file_name in missing_files:
            print(f"   - {file_name}")
        return False
    
    print("✅ 所有必需文件都存在")
    return True

def main():
    """主函数"""
    print("🚀 开始打包采购部绩效数据拉取系统")
    print("=" * 60)
    
    # 0. 验证必需文件
    if not verify_required_files():
        print("\n❌ 请确保所有必需文件都存在后重试")
        return
    
    # 1. 检查 PyInstaller
    if not check_pyinstaller():
        return
    
    # 2. 清理构建目录
    clean_build_dirs()
    
    # 3. 创建规格文件
    create_spec_file()
    
    # 4. 构建 exe
    if build_exe():
        # 5. 复制配置文件
        copy_config_files()
        
        print("\n" + "=" * 60)
        print("✅ 打包完成!")
        print("📁 输出目录: dist/")
        print("🎯 可执行文件: dist/采购绩效数据同步.exe")
        print("\n📋 外部配置文件:")
        print("   - config.json (配置文件)")
        print("\n使用方法:")
        print("  正常运行: 采购绩效数据同步.exe")
        print("  指定日期: 采购绩效数据同步.exe -d 2025-07-20")
        print("\n💡 特性:")
        print("  - 程序运行完成后会暂停，按回车键退出")
        print("  - 控制台窗口不会自动关闭，方便查看运行结果")
        print("\n⚠️  注意: 请确保config.json文件与exe文件在同一目录下")
        print("=" * 60)
    else:
        print("\n❌ 打包失败")

if __name__ == "__main__":
    main()
