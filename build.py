#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采购部绩效数据拉取系统打包脚本
使用 PyInstaller 将 Python 程序打包成 exe 文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查是否安装了 PyInstaller"""
    try:
        import PyInstaller
        print("✅ PyInstaller 已安装")
        return True
    except ImportError:
        print("❌ PyInstaller 未安装")
        print("正在安装 PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller 安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller 安装失败")
            return False

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)

def create_spec_file():
    """创建 PyInstaller 规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['purchase_checker_simplified.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('dingtalk_sheet_utils.py', '.'),
    ],
    hiddenimports=[
        'pandas',
        'requests',
        'openpyxl',
        'xlrd',
        'urllib3',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='采购绩效数据同步',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('build_spec.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 创建规格文件: build_spec.spec")

def build_exe():
    """构建 exe 文件"""
    print("🔨 开始构建 exe 文件...")
    
    try:
        # 使用规格文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "build_spec.spec"]
        subprocess.check_call(cmd)
        print("✅ exe 文件构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def copy_config_files():
    """复制配置文件到输出目录"""
    dist_dir = Path("dist")
    if dist_dir.exists():
        config_files = ['config.json']
        for config_file in config_files:
            if os.path.exists(config_file):
                shutil.copy2(config_file, dist_dir)
                print(f"📋 复制配置文件: {config_file}")

def main():
    """主函数"""
    print("🚀 开始打包采购部绩效数据拉取系统")
    print("=" * 60)
    
    # 1. 检查 PyInstaller
    if not check_pyinstaller():
        return
    
    # 2. 清理构建目录
    clean_build_dirs()
    
    # 3. 创建规格文件
    create_spec_file()
    
    # 4. 构建 exe
    if build_exe():
        # 5. 复制配置文件
        copy_config_files()
        
        print("\n" + "=" * 60)
        print("✅ 打包完成!")
        print("📁 输出目录: dist/")
        print("🎯 可执行文件: dist/售后绩效数据同步.exe")
        print("\n使用方法:")
        print("  正常运行: 售后绩效数据同步.exe")
        print("  指定日期: 售后绩效数据同步.exe -d 2025-07-20")
        print("=" * 60)
    else:
        print("\n❌ 打包失败")

if __name__ == "__main__":
    main()
