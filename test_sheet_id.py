#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试钉钉表格ID的工作表数量

用于验证表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
"""

import json
from dingtalk_sheet_utils import DingTalkSheetUtils


def test_sheet_count():
    """测试指定表格ID有几个工作表"""
    print("🧪 开始测试表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE")
    print("=" * 60)

    # 加载配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✅ 配置文件加载成功")
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return

    # 创建测试配置，强制使用指定的表格ID
    test_config = config.copy()
    test_config['dingtalk']['current_environment'] = 'test'
    test_config['dingtalk']['sheet']['test_sheet']['sheet_id'] = 'gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE'

    # 初始化钉钉表格工具
    try:
        sheet_utils = DingTalkSheetUtils(test_config)
        print("✅ 钉钉表格工具初始化成功")
    except Exception as e:
        print(f"❌ 钉钉表格工具初始化失败: {e}")
        return

    # 获取工作表列表
    print("\n📋 获取工作表列表")
    print("-" * 40)
    try:
        sheets = sheet_utils.get_sheets_list()
        if sheets:
            print(f"✅ 成功获取到 {len(sheets)} 个工作表:")
            for i, sheet in enumerate(sheets):
                sheet_id = sheet.get('id', 'N/A')
                sheet_name = sheet.get('name', 'N/A')
                print(f"  {i+1}. ID: {sheet_id}")
                print(f"     名称: {sheet_name}")
        else:
            print("⚠️ 未获取到任何工作表")
    except Exception as e:
        print(f"❌ 获取工作表列表失败: {e}")

    print("\n" + "=" * 60)
    print("🎯 测试完成！")


if __name__ == "__main__":
    test_sheet_count()
