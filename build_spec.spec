# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['purchase_checker_simplified.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('dingtalk_sheet_utils.py', '.'),
    ],
    hiddenimports=[
        'pandas',
        'requests',
        'openpyxl',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的大型库
        'matplotlib',
        'scipy',
        'numpy.distutils',
        'numpy.f2py',
        'numpy.testing',
        'pandas.plotting',
        'pandas.tests',
        'pandas.io.formats.style',
        'pandas.io.clipboard',
        'pandas.io.html',
        'pandas.io.sql',
        'pandas.io.sas',
        'pandas.io.spss',
        'pandas.io.stata',
        'pandas.io.parquet',
        'pandas.io.feather',
        'pandas.io.orc',
        'pandas.io.gbq',
        'pandas.plotting._matplotlib',
        'xlrd',
        'xlwt',
        'xlsxwriter',
        'pytz',
        'dateutil.tz',
        'IPython',
        'jupyter',
        'notebook',
        'tkinter',
        'turtle',
        'pydoc',
        'doctest',
        'unittest',
        'test',
        'tests',
        'distutils',
        'setuptools',
        'pip',
        'wheel',
        'pkg_resources',
        # 排除开发和测试相关模块
        'pytest',
        'coverage',
        'flake8',
        'black',
        'mypy',
        # 排除不需要的编码库
        'encodings.cp1006',
        'encodings.cp1026',
        'encodings.cp1140',
        'encodings.cp1250',
        'encodings.cp1251',
        'encodings.cp1252',
        'encodings.cp1253',
        'encodings.cp1254',
        'encodings.cp1255',
        'encodings.cp1256',
        'encodings.cp1257',
        'encodings.cp1258',
        'encodings.cp424',
        'encodings.cp437',
        'encodings.cp500',
        'encodings.cp720',
        'encodings.cp737',
        'encodings.cp775',
        'encodings.cp850',
        'encodings.cp852',
        'encodings.cp855',
        'encodings.cp856',
        'encodings.cp857',
        'encodings.cp858',
        'encodings.cp860',
        'encodings.cp861',
        'encodings.cp862',
        'encodings.cp863',
        'encodings.cp864',
        'encodings.cp865',
        'encodings.cp866',
        'encodings.cp869',
        'encodings.cp874',
        'encodings.cp875',
        'encodings.cp932',
        'encodings.cp949',
        'encodings.cp950',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
    optimize=2,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='采购绩效数据同步',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=True,
    upx_exclude=[
        'vcruntime140.dll',
        'msvcp140.dll',
        'api-ms-win-*.dll',
    ],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
